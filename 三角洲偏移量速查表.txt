===============================================
    三角洲 Delta Force 8.4 偏移量速查表
===============================================

【基础信息】
游戏版本: Delta Force 8.4
模块名称: DeltaForceClient-Win64-Shipping.exe
UWorld偏移: 0x1264C588
更新时间: 2024-12-06
验证状态: ✅ 已确认有效

===============================================
【核心指针链路径】
===============================================

模块基址 → UWorld → PersistentLevel → ActorArray → Actor实体

完整路径:
getAddress("DeltaForceClient-Win64-Shipping.exe")
  ↓ +0x1264C588
UWorld
  ↓ +0x108  
PersistentLevel
  ↓ +0xA8
ActorArray (指针)
  ↓ +索引*8
Actor实体

===============================================
【基础结构偏移】
===============================================

// 世界结构
PersistentLevel     = 0x108    // UWorld → PersistentLevel
ActorArray          = 0xA8     // PersistentLevel → Actor数组指针  
ActorCount          = 0xB0     // PersistentLevel → Actor数量

// Actor基础
ObjectID            = 0x24     // Actor → 对象ID (Int32)
RootComponent       = 0x188    // Actor → 根组件指针 (QWORD)

===============================================
【血量系统 - 已验证】
===============================================

指针链: Actor → HealthComponent → HealthSet → 血量数据

HealthComponent     = 0xF00    // Actor → 血量组件指针
HealthSet           = 0x250    // HealthComponent → 血量数据集

// 血量数据 (基于HealthSet)
Health              = 0x5C     // 当前血量 (Float)
MaxHealth           = 0x7C     // 最大血量 (Float)  
ArmorHealth         = 0xBC     // 护甲当前耐久 (Float)
ArmorMaxHealth      = 0xDC     // 护甲最大耐久 (Float)
HelmetArmorHealth   = 0xFC     // 头盔当前耐久 (Float)
HelmetMaxHealth     = 0x11C    // 头盔最大耐久 (Float)

完整读取示例:
healthComp = readQword(actor + 0xF00)
healthSet = readQword(healthComp + 0x250)  
currentHP = readFloat(healthSet + 0x5C)

===============================================
【位置系统 - 已验证】
===============================================

指针链: Actor → RootComponent → RelativeLocation

RootComponent       = 0x188    // Actor → 根组件指针
RelativeLocation    = 0x220    // RootComponent → 位置向量

// 坐标读取 (基于RelativeLocation)
X坐标 = RelativeLocation + 0x0  (Float)
Y坐标 = RelativeLocation + 0x4  (Float)
Z坐标 = RelativeLocation + 0x8  (Float)

完整读取示例:
rootComp = readQword(actor + 0x188)
x = readFloat(rootComp + 0x220)
y = readFloat(rootComp + 0x224)  
z = readFloat(rootComp + 0x228)

===============================================
【移动系统 - 已验证】
===============================================

指针链: Actor → CharacterMovement → 移动数据

CharacterMovement   = 0x3F0    // Actor → 移动组件指针
Velocity            = 0x168    // CharacterMovement → 速度向量
MaxWalkSpeed        = 0x1A4    // CharacterMovement → 最大速度

// 速度读取 (基于Velocity)
VX = Velocity + 0x0  (Float)
VY = Velocity + 0x4  (Float)  
VZ = Velocity + 0x8  (Float)

注意: 速度值需要除以100转换为m/s

===============================================
【状态系统 - 已验证】
===============================================

// 玩家状态
PlayerState         = 0x3A0    // Actor → 玩家状态指针 (QWORD)
BlackboardComponent = 0xE68    // Actor → AI黑板指针 (QWORD)
PlayerController    = 0x3A8    // Actor → 控制器指针 (QWORD)

// 视角控制 (基于PlayerController)
ControlRotation     = 0x298    // PlayerController → 旋转数据

// 视角读取 (基于ControlRotation)  
Pitch = ControlRotation + 0x0  (Float)
Yaw   = ControlRotation + 0x4  (Float)
Roll  = ControlRotation + 0x8  (Float)

人机判断逻辑 (基于sjze.txt验证):
if PlayerState > 100000 then "真实玩家"
elseif PlayerState > 0 and PlayerState < 100000 then "AI"  
else "无效实体"

实体筛选流程:
1. isPlayerType() - 检查是否为玩家类型
2. getTeam() != 本地队伍 - 过滤队友
3. getMesh() > 100000 - 检查Mesh组件有效性
4. getHealthy() > 0 - 检查血量状态

===============================================
【队伍系统 - 已验证】
===============================================

指针链: Actor → TeamComponent → 队伍数据

TeamComponent       = 0xF08    // Actor → 队伍组件指针
TeamId              = 0x110    // TeamComponent → 队伍ID (Int32)
CampId              = 0x114    // TeamComponent → 阵营ID (Int32)

完整读取示例:
teamComp = readQword(actor + 0xF08)
teamId = readInteger(teamComp + 0x110)
campId = readInteger(teamComp + 0x114)

===============================================
【武器系统 - 已验证】
===============================================

指针链: Actor → CacheCurWeapon → 武器数据

CacheCurWeapon      = 0x1560   // Actor → 当前武器指针
WeaponID            = 0x828    // CacheCurWeapon → 武器ID (Int32)
WeaponAmmoCount     = 0x50     // CacheCurWeapon → 弹药数 (Int32)
BulletSpeed         = 0x4C8    // CacheCurWeapon → 弹速 (Float)
WeaponDamage        = 0x4CC    // CacheCurWeapon → 伤害 (Float)

完整读取示例:
weaponPtr = readQword(actor + 0x1560)
weaponId = readInteger(weaponPtr + 0x828)
ammo = readInteger(weaponPtr + 0x50)

===============================================
【装备系统 - 部分验证】
===============================================

指针链: Actor → CharacterEquipComponent → EquipmentInfoArray → 装备槽

CharacterEquipComponent = 0x1F50   // Actor → 装备组件指针
EquipmentInfoArray      = 0x1E0    // 装备组件 → 装备数组指针

// 装备槽偏移 (基于EquipmentInfoArray)
HelmetEquip         = 0x30     // 头盔装备槽
ArmorEquip          = 0xF0     // 护甲装备槽
VestEquip           = 0x150    // 胸挂装备槽  
BackpackEquip       = 0x180    // 背包装备槽

// 每个装备槽数据结构:
装备ID = 槽位偏移 + 0x0  (Int32)
装备等级 = 槽位偏移 + 0x4  (Int32)

完整读取示例:
equipComp = readQword(actor + 0x1F50)
equipArray = readQword(equipComp + 0x1E0)
helmetId = readInteger(equipArray + 0x30)
helmetLevel = readInteger(equipArray + 0x34)

注意: AI的装备数据可能为空

===============================================
【矩阵系统 - 已验证】
===============================================

// 视图矩阵 (3D到2D坐标转换)
ViewMatrix          = 0x7FF5F080   // 视图矩阵固定地址
MatrixSize          = 64           // 4x4矩阵，16个float值
UpdateFrequency     = "实时"        // 每帧更新

// 矩阵读取示例
function readViewMatrix()
    local matrix = {}
    local matrixAddr = 0x7FF5F080
    for i = 0, 15 do
        matrix[i + 1] = readFloat(matrixAddr + i * 4)
    end
    return matrix
end

// 3D到2D转换示例
function worldToScreen(worldX, worldY, worldZ, matrix)
    local x, y, z, w = worldX, worldY, worldZ, 1.0
    
    -- 矩阵变换
    local x_prime = x * matrix[1] + y * matrix[5] + z * matrix[9] + w * matrix[13]
    local y_prime = x * matrix[2] + y * matrix[6] + z * matrix[10] + w * matrix[14]
    local w_prime = x * matrix[4] + y * matrix[8] + z * matrix[12] + w * matrix[16]
    
    -- 透视除法
    if math.abs(w_prime) < 0.001 then return nil, nil end
    local ndc_x = x_prime / w_prime
    local ndc_y = y_prime / w_prime
    
    -- 屏幕坐标转换
    local screenWidth, screenHeight = 1920, 1080
    local pixel_x = (ndc_x + 1.0) * screenWidth / 2.0
    local pixel_y = (1.0 - ndc_y) * screenHeight / 2.0
    
    return pixel_x, pixel_y
end

应用场景:
- ESP透视 (敌人方框、血量条)
- 自动瞄准 (精确计算瞄准点)
- 2D雷达 (世界坐标转雷达坐标)
- 轨迹预测 (子弹飞行路径)

===============================================
【其他组件偏移】
===============================================

MeshComponent       = 0x3E8    // Actor → 网格组件指针
PlayerName          = 0x318    // 玩家名称 (待验证)

===============================================
【数据类型说明】
===============================================

QWORD   = 8字节指针 (readQword)
Float   = 4字节浮点数 (readFloat)  
Int32   = 4字节整数 (readInteger)
Vector3 = 3个连续的Float (X,Y,Z)
Rotator = 3个连续的Float (Pitch,Yaw,Roll)

===============================================
【安全读取模板】
===============================================

function safeReadQword(addr)
    if not addr or addr == 0 or addr < 0x1000 then return 0 end
    local success, result = pcall(readQword, addr)
    return success and result or 0
end

function safeReadFloat(addr)
    if not addr or addr == 0 or addr < 0x1000 then return 0 end
    local success, result = pcall(readFloat, addr)
    return success and result or 0
end

function safeReadInteger(addr)
    if not addr or addr == 0 or addr < 0x1000 then return 0 end
    local success, result = pcall(readInteger, addr)
    return success and result or 0
end

===============================================
【快速验证脚本】
===============================================

-- 获取基础地址
moduleBase = getAddress("DeltaForceClient-Win64-Shipping.exe")
uworld = readQword(moduleBase + 0x1264C588)
persistentLevel = readQword(uworld + 0x108)
actorArray = readQword(persistentLevel + 0xA8)
actorCount = readInteger(persistentLevel + 0xB0)

-- 测试第一个Actor
actor = readQword(actorArray)
print("Actor地址:", string.format("0x%X", actor))

-- 测试血量
healthComp = readQword(actor + 0xF00)
if healthComp > 0 then
    healthSet = readQword(healthComp + 0x250)
    if healthSet > 0 then
        hp = readFloat(healthSet + 0x5C)
        print("血量:", hp)
    end
end

===============================================
【版本更新记录】
===============================================

v2.0 (2024-12-06):
+ 添加移动系统完整偏移
+ 添加视角控制偏移  
+ 完善装备系统偏移
+ 添加武器伤害偏移
+ 优化人机判断逻辑

v1.0 (2024-12-05):
+ 初始版本，基础偏移量

===============================================
【重要提醒】
===============================================

1. 游戏更新后偏移量可能失效，需要重新验证
2. 使用前请先测试基础地址是否有效
3. 建议使用安全读取函数避免程序崩溃
4. AI实体的某些数据可能为空或不同
5. 本文档仅供技术研究使用

===============================================