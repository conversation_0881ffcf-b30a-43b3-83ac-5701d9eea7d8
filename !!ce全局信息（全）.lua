-- 增强版三角洲数据测试 - 添加更多测试项目
local baseModule = "DeltaForceClient-Win64-Shipping.exe"
local UWorldOffset = 0x1264C588

local offsets = {
    PersistentLevel = 0x108,
    ActorArray = 0xA8,
    ActorCount = 0xB0,
    ObjectID = 0x24,
    RootComponent = 0x188,
    HealthComponent = 0xF00,
    PlayerState = 0x3A0,
    BlackboardComponent = 0xE68,
    TeamComponent = 0xF08,
    MeshComponent = 0x3E8,
    RelativeLocation = 0x220,
    HealthSet = 0x250,
    Health = 0x5C,
    MaxHealth = 0x7C,
    ArmorHealth = 0xBC,        -- 护甲当前耐久
    ArmorMaxHealth = 0xDC,     -- 护甲最大耐久
    HelmetArmorHealth = 0xFC,  -- 头盔当前耐久
    HelmetMaxHealth = 0x11C,   -- 头盔最大耐久
    TeamId = 0x110,
    CampId = 0x114,


    -- 移动相关偏移
    CharacterMovement = 0x3F0,
    Velocity = 0x168,
    MaxWalkSpeed = 0x1A4,
    -- 视角相关偏移
    PlayerController = 0x3A8,
    ControlRotation = 0x298,
    -- 名称相关偏移
    PlayerName = 0x318
}

function safeReadQword(addr)
    if not addr or addr == 0 or addr < 0x1000 then return 0 end
    local success, result = pcall(readQword, addr)
    return success and result or 0
end

function safeReadFloat(addr)
    if not addr or addr == 0 or addr < 0x1000 then return 0 end
    local success, result = pcall(readFloat, addr)
    return success and result or 0
end

function safeReadInteger(addr)
    if not addr or addr == 0 or addr < 0x1000 then return 0 end
    local success, result = pcall(readInteger, addr)
    return success and result or 0
end

function safeReadString(addr, maxLen)
    if not addr or addr == 0 or addr < 0x1000 then return "" end
    local success, result = pcall(readString, addr, maxLen or 32)
    return success and result or ""
end

-- 计算距离
function calculateDistance(x1, y1, z1, x2, y2, z2)
    local dx = x2 - x1
    local dy = y2 - y1
    local dz = z2 - z1
    return math.sqrt(dx*dx + dy*dy + dz*dz) / 100
end

-- 计算速度
function calculateSpeed(vx, vy, vz)
    return math.sqrt(vx*vx + vy*vy + vz*vz) / 100
end

-- 获取完整实体数据
function getCompleteEntityData(actorPtr)
    local entity = {
        address = actorPtr,
        objectID = 0,
        
        -- 血量信息
        health = 0,
        maxHealth = 0,
        armor = 0,
        armorMax = 0,
        helmet = 0,
        helmetMax = 0,
        
        -- 位置信息
        x = 0, y = 0, z = 0,
        
        -- 速度信息
        vx = 0, vy = 0, vz = 0,
        speed = 0,
        maxWalkSpeed = 0,
        
        -- 视角信息
        pitch = 0, yaw = 0, roll = 0,
        
        -- 队伍信息
        teamId = -1,
        campId = -1,
        
        -- 状态信息
        playerState = 0,
        blackboard = 0,
        playerController = 0,
        

        
        -- 玩家名称
        playerName = "",
        
        -- 分类信息
        entityType = "未知",
        isValid = false,
        
        -- 测试结果
        testResults = {
            hasHealth = false,
            hasPosition = false,
            hasVelocity = false,
            hasTeamInfo = false,
            hasController = false
        }
    }
    
    -- 获取基础信息
    entity.objectID = safeReadInteger(actorPtr + offsets.ObjectID)
    
    -- 获取血量信息
    local healthComp = safeReadQword(actorPtr + offsets.HealthComponent)
    if healthComp > 0 then
        local healthSet = safeReadQword(healthComp + offsets.HealthSet)
        if healthSet > 0 then
            entity.health = safeReadFloat(healthSet + offsets.Health)
            entity.maxHealth = safeReadFloat(healthSet + offsets.MaxHealth)
            entity.armor = safeReadFloat(healthSet + offsets.ArmorHealth)
            entity.armorMax = safeReadFloat(healthSet + offsets.ArmorMaxHealth)
            entity.helmet = safeReadFloat(healthSet + offsets.HelmetArmorHealth)
            entity.helmetMax = safeReadFloat(healthSet + offsets.HelmetMaxHealth)
            
            if entity.health > 0 then
                entity.testResults.hasHealth = true
            end
        end
    end
    
    -- 获取位置信息
    local rootComp = safeReadQword(actorPtr + offsets.RootComponent)
    if rootComp > 0 then
        entity.x = safeReadFloat(rootComp + offsets.RelativeLocation)
        entity.y = safeReadFloat(rootComp + offsets.RelativeLocation + 4)
        entity.z = safeReadFloat(rootComp + offsets.RelativeLocation + 8)
        
        if entity.x ~= 0 or entity.y ~= 0 then
            entity.testResults.hasPosition = true
        end
    end
    
    -- 获取移动信息
    local movementComp = safeReadQword(actorPtr + offsets.CharacterMovement)
    if movementComp > 0 then
        entity.vx = safeReadFloat(movementComp + offsets.Velocity)
        entity.vy = safeReadFloat(movementComp + offsets.Velocity + 4)
        entity.vz = safeReadFloat(movementComp + offsets.Velocity + 8)
        entity.speed = calculateSpeed(entity.vx, entity.vy, entity.vz)
        entity.maxWalkSpeed = safeReadFloat(movementComp + offsets.MaxWalkSpeed)
        
        if entity.speed > 0 or entity.maxWalkSpeed > 0 then
            entity.testResults.hasVelocity = true
        end
    end
    
    -- 获取状态信息
    entity.playerState = safeReadQword(actorPtr + offsets.PlayerState)
    entity.blackboard = safeReadQword(actorPtr + offsets.BlackboardComponent)
    entity.playerController = safeReadQword(actorPtr + offsets.PlayerController)
    
    if entity.playerController > 0 then
        entity.testResults.hasController = true
        
        -- 获取视角信息
        entity.pitch = safeReadFloat(entity.playerController + offsets.ControlRotation)
        entity.yaw = safeReadFloat(entity.playerController + offsets.ControlRotation + 4)
        entity.roll = safeReadFloat(entity.playerController + offsets.ControlRotation + 8)
    end
    
    -- 获取队伍信息
    local teamComp = safeReadQword(actorPtr + offsets.TeamComponent)
    if teamComp > 0 then
        entity.teamId = safeReadInteger(teamComp + offsets.TeamId)
        entity.campId = safeReadInteger(teamComp + offsets.CampId)
        
        if entity.teamId >= 0 then
            entity.testResults.hasTeamInfo = true
        end
    end
    

    

    
    -- 实体分类
    if entity.health > 0 and entity.health <= 100 then
        if entity.playerState > 0 then
            entity.entityType = "真实玩家"
            entity.isValid = true
        elseif entity.blackboard > 0 then
            entity.entityType = "AI"
            entity.isValid = true
        else
            entity.entityType = "未知生物"
        end
    end
    
    return entity
end

function runEnhancedTest()
    local moduleBase = getAddress(baseModule)
    if not moduleBase or moduleBase == 0 then
        showMessage("错误: 无法获取游戏模块基址")
        return
    end
    
    local uworld = safeReadQword(moduleBase + UWorldOffset)
    if uworld == 0 then
        showMessage("错误: 无法获取UWorld地址")
        return
    end
    
    local persistentLevel = safeReadQword(uworld + offsets.PersistentLevel)
    if persistentLevel == 0 then
        showMessage("错误: 无法获取PersistentLevel")
        return
    end
    
    local actorArrayPtr = safeReadQword(persistentLevel + offsets.ActorArray)
    local actorCount = safeReadInteger(persistentLevel + offsets.ActorCount)
    
    if actorArrayPtr == 0 or actorCount == 0 then
        showMessage("错误: Actor数组无效")
        return
    end
    
    -- 扫描实体
    local entities = {}
    local scanCount = math.min(actorCount, 5000)
    
    for i = 0, scanCount - 1 do
        local actorPtr = safeReadQword(actorArrayPtr + i * 8)
        if actorPtr > 0 then
            local entity = getCompleteEntityData(actorPtr)
            if entity.isValid then
                table.insert(entities, entity)
            end
        end
    end
    
    -- 分类统计
    local players = {}
    local ais = {}
    local localPlayer = nil
    
    for _, entity in ipairs(entities) do
        if entity.entityType == "真实玩家" then
            table.insert(players, entity)
            if not localPlayer or entity.playerState > localPlayer.playerState then
                localPlayer = entity
            end
        elseif entity.entityType == "AI" then
            table.insert(ais, entity)
        end
    end
    
    -- 数据质量统计
    local qualityStats = {
        hasHealth = 0,
        hasPosition = 0,
        hasVelocity = 0,
        hasTeamInfo = 0,
        hasController = 0
    }
    
    for _, entity in ipairs(entities) do
        if entity.testResults.hasHealth then qualityStats.hasHealth = qualityStats.hasHealth + 1 end
        if entity.testResults.hasPosition then qualityStats.hasPosition = qualityStats.hasPosition + 1 end
        if entity.testResults.hasVelocity then qualityStats.hasVelocity = qualityStats.hasVelocity + 1 end
        if entity.testResults.hasTeamInfo then qualityStats.hasTeamInfo = qualityStats.hasTeamInfo + 1 end
        if entity.testResults.hasController then qualityStats.hasController = qualityStats.hasController + 1 end
    end
    
    -- 构建结果文本
    local resultText = "=== 三角洲增强数据测试结果 ===\n"
    resultText = resultText .. "模块基址: " .. string.format("0x%X", moduleBase) .. "\n"
    resultText = resultText .. "UWorld: " .. string.format("0x%X", uworld) .. "\n"
    resultText = resultText .. "Actor数量: " .. actorCount .. "\n"
    resultText = resultText .. "扫描数量: " .. scanCount .. "\n"
    resultText = resultText .. "有效实体: " .. #entities .. " 个\n"
    resultText = resultText .. "真实玩家: " .. #players .. " 个\n"
    resultText = resultText .. "AI实体: " .. #ais .. " 个\n\n"
    
    -- 数据质量报告
    resultText = resultText .. "=== 数据质量报告 ===\n"
    if #entities > 0 then
        resultText = resultText .. "血量数据: " .. qualityStats.hasHealth .. "/" .. #entities .. " (" .. string.format("%.1f%%", (qualityStats.hasHealth/#entities)*100) .. ")\n"
        resultText = resultText .. "位置数据: " .. qualityStats.hasPosition .. "/" .. #entities .. " (" .. string.format("%.1f%%", (qualityStats.hasPosition/#entities)*100) .. ")\n"
        resultText = resultText .. "速度数据: " .. qualityStats.hasVelocity .. "/" .. #entities .. " (" .. string.format("%.1f%%", (qualityStats.hasVelocity/#entities)*100) .. ")\n"
        resultText = resultText .. "队伍数据: " .. qualityStats.hasTeamInfo .. "/" .. #entities .. " (" .. string.format("%.1f%%", (qualityStats.hasTeamInfo/#entities)*100) .. ")\n"
        resultText = resultText .. "控制器数据: " .. qualityStats.hasController .. "/" .. #entities .. " (" .. string.format("%.1f%%", (qualityStats.hasController/#entities)*100) .. ")\n\n"
    end
    
    -- 所有玩家详细信息
    if #players > 0 then
        resultText = resultText .. "=== 所有玩家详细信息 ===\n"
        
        for i, player in ipairs(players) do
            -- 判断是否为本地玩家
            local isLocalPlayer = (localPlayer and player.address == localPlayer.address)
            local playerLabel = isLocalPlayer and "本地玩家" or "玩家"
            
            resultText = resultText .. string.format("%s%d:\n", playerLabel, i)
            resultText = resultText .. string.format("  地址: 0x%X | ObjectID: %d\n", player.address, player.objectID)
            resultText = resultText .. string.format("  血量: %.1f/%.1f | 护甲: %.1f/%.1f | 头盔: %.1f/%.1f\n", 
                player.health, player.maxHealth, player.armor, player.armorMax, player.helmet, player.helmetMax)
            resultText = resultText .. string.format("  位置: (%.0f, %.0f, %.0f)\n", player.x, player.y, player.z)
            resultText = resultText .. string.format("  速度: %.1f m/s (最大: %.1f m/s)\n", player.speed, player.maxWalkSpeed/100)
            resultText = resultText .. string.format("  队伍: %d | 阵营: %d\n", player.teamId, player.campId)
            resultText = resultText .. string.format("  PlayerState: 0x%X\n", player.playerState)
            resultText = resultText .. string.format("  Controller: 0x%X\n", player.playerController)
            
            -- 视角信息
            if player.testResults.hasController then
                resultText = resultText .. string.format("  视角: (%.1f, %.1f, %.1f)\n", player.pitch, player.yaw, player.roll)
            end
            
            -- 计算与其他玩家的距离
            if #players > 1 then
                local distances = {}
                for j, otherPlayer in ipairs(players) do
                    if i ~= j then
                        local dist = calculateDistance(
                            player.x, player.y, player.z,
                            otherPlayer.x, otherPlayer.y, otherPlayer.z
                        )
                        table.insert(distances, string.format("玩家%d: %.1fm", j, dist))
                    end
                end
                if #distances > 0 then
                    resultText = resultText .. "  与其他玩家距离: " .. table.concat(distances, ", ") .. "\n"
                end
            end
            
            -- 如果是本地玩家，添加特殊标记
            if isLocalPlayer then
                resultText = resultText .. "  ⭐ 这是本地玩家\n"
            end
            
            resultText = resultText .. "\n"
        end
    else
        resultText = resultText .. "=== 玩家信息 ===\n"
        resultText = resultText .. "❌ 未找到任何真实玩家\n\n"
    end
    
    -- AI详细信息
    if #ais > 0 then
        resultText = resultText .. "=== AI详细信息 ===\n"
        
        -- 使用第一个玩家作为距离参考点
        local referencePlayer = #players > 0 and players[1] or nil
        
        for i, ai in ipairs(ais) do
            local distance = 0
            if referencePlayer then
                distance = calculateDistance(
                    referencePlayer.x, referencePlayer.y, referencePlayer.z,
                    ai.x, ai.y, ai.z
                )
            end
            
            resultText = resultText .. string.format("AI%d:\n", i)
            resultText = resultText .. string.format("  地址: 0x%X | ObjectID: %d\n", ai.address, ai.objectID)
            resultText = resultText .. string.format("  血量: %.1f/%.1f | 护甲: %.1f/%.1f | 头盔: %.1f/%.1f\n", 
                ai.health, ai.maxHealth, ai.armor, ai.armorMax, ai.helmet, ai.helmetMax)
            resultText = resultText .. string.format("  位置: (%.0f, %.0f, %.0f)", ai.x, ai.y, ai.z)
            
            if referencePlayer then
                resultText = resultText .. string.format(" | 距离: %.1fm\n", distance)
            else
                resultText = resultText .. "\n"
            end
            
            resultText = resultText .. string.format("  速度: %.1f m/s (最大: %.1f m/s)\n", ai.speed, ai.maxWalkSpeed/100)
            resultText = resultText .. string.format("  队伍: %d | 阵营: %d\n", ai.teamId, ai.campId)
            resultText = resultText .. string.format("  状态: PlayerState=0x%X | Blackboard=0x%X\n", 
                ai.playerState, ai.blackboard)
            
            if ai.testResults.hasController then
                resultText = resultText .. string.format("  控制器: 0x%X | 视角=(%.1f, %.1f, %.1f)\n", 
                    ai.playerController, ai.pitch, ai.yaw, ai.roll)
            end
            
            resultText = resultText .. "\n"
        end
    else
        resultText = resultText .. "=== AI信息 ===\n"
        resultText = resultText .. "❌ 未找到任何AI实体\n\n"
    end

    
    -- 总体评分
    local totalScore = 0
    local maxScore = 6
    
    if #entities > 0 then totalScore = totalScore + 1 end
    if qualityStats.hasHealth > 0 then totalScore = totalScore + 1 end
    if qualityStats.hasPosition > 0 then totalScore = totalScore + 1 end
    if qualityStats.hasVelocity > 0 then totalScore = totalScore + 1 end

    if qualityStats.hasTeamInfo > 0 then totalScore = totalScore + 1 end
    if qualityStats.hasController > 0 then totalScore = totalScore + 1 end
    
    resultText = resultText .. "\n=== 总体评分 ===\n"
    resultText = resultText .. "数据完整性: " .. totalScore .. "/" .. maxScore .. " (" .. string.format("%.1f%%", (totalScore/maxScore)*100) .. ")\n"
    
    if totalScore >= 6 then
        resultText = resultText .. "🎉 数据基质状态: 优秀\n"
    elseif totalScore >= 4 then
        resultText = resultText .. "👍 数据基质状态: 良好\n"
    elseif totalScore >= 2 then
        resultText = resultText .. "⚠️ 数据基质状态: 一般\n"
    else
        resultText = resultText .. "❌ 数据基质状态: 需要改进\n"
    end
    
    showMessage(resultText)
end

runEnhancedTest()
