																/*
const uint32_t OwningGameInstance = 0x1B0; //432
const uint32_t PersistentLevel = 0x108; //264
const uint32_t Levels = 0x178; //376
const uint32_t GameState = 0x158; //344
const uint32_t LocalPlayers = 0x48; //72
const uint32_t PlayerController = 0x38; //56
const uint32_t PlayerCameraManager = 0x430; //1072
const uint32_t AcknowledgedPawn = 0x410; //1040
const uint32_t InputYawScale = 0x5AC; //1452
const uint32_t InputPitchScale = 0x5B0; //1456
const uint32_t MyHUD = 0x428; //1064
const uint32_t RootComponent = 0x188; //392
const uint32_t Actor_PlayerState = 0x3A0; //928
const uint32_t PlayerNamePrivate = 0x488; //1160
const uint32_t HeroID = 0x9B8; //2488
const uint32_t ExitState = 0xD60; //3424
const uint32_t AController_PlayerState = 0x388; //904
const uint32_t ControlRotation = 0x3E8; //1000
const uint32_t RelativeLocation = 0x16C; //364
const uint32_t BoundsScale = 0x2E4; //740
const uint32_t Mesh = 0x3E8; //1000
const uint32_t TeamId = 0x67C; //1660
const uint32_t bFinishGame = 0x4D8; //1240
const uint32_t HealthComp = 0xF00; //3840
const uint32_t HealthSet = 0x250; //592
const uint32_t Health = 0x48; //72
const uint32_t ImpendingDeathHealth = 0x168; //360
const uint32_t ArmorHealth = 0xA8; //168
const uint32_t HelmetArmorHealth = 0xE8; //232
const uint32_t HeadHealth = 0x1E8; //488
const uint32_t IsAlive = 0x1A8; //424
const uint32_t PickupItemInfo = 0xE80; //3712
const uint32_t PlayerName = 0x1DD0; //7632
const uint32_t bIsAI = 0x1E50; //7760
const uint32_t RepItemArray = 0x17C8; //6088
const uint32_t TeamComp = 0xF08; //3848
const uint32_t CharacterEquipComponent = 0x2D40; //11584
const uint32_t EquipedArmorInfoArray = 0x1F0; //496