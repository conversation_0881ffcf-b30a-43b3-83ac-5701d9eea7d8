																/*struct _Offsets {                                 
     uint64_t GName = 0x0000000152F50400;                        
     uint64_t UWorld = 0x000000015264C588;                        
       uint64_t Gobject = 0x0000000000000012;                        
       uint64_t GobjectArray = 0x0000000152F69A50;                   
       struct {                                      
           uint16_t Objects = 0x10;                    
           uint16_t NumChunks = 0x8;                  
           uint16_t NumElements = 0x0;                
       } TUObjectArray;                              
       struct {                                      
           uint16_t Size = 0x20;                       
           uint16_t Object = 0x0;                     
       } FUObjectItem;                               
       struct {                                      
           uint16_t Class = 0x10;                      
           uint16_t Name = 0x24;                       
           uint16_t Outer = 0x18;                      
       } UObject;                                    
       struct {                                      
           uint16_t SuperStruct = 0x50;                
           uint16_t ChildProperties = 0x78;            
           uint16_t PropertiesSize = 0x48;             
       } UStruct;                                    
       struct {                                      
           uint16_t Name = 0x0;                       
           uint16_t Value = 0x8;                      
           uint16_t Size = 0x10;                       
           uint16_t Names = 0x48;                      
       } UEnum;                                      
       struct {                                      
           uint16_t Func = 0xf0;                       
       } UFunction;                                  
       struct {                                      
           uint16_t Class = 0x20;                      
           uint16_t Next = 0x18;                       
           uint16_t Name = 0x28;                       
       } FField;                                     
       struct {                                      
           uint16_t ElementSize = 0x40;                
           uint16_t Offset = 0x54;                     
           uint16_t Size = 0x88;                       
       } FProperty;                                  
   }                                                 
   
